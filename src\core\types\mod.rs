/// PumpFun解析器类型定义
/// 
/// 定义解析过程中使用的数据结构和枚举类型

use serde::{Deserialize, Serialize};
use solana_program::pubkey::Pubkey;
use chrono::{DateTime, Utc};
use rust_decimal::Decimal;

use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::time::{Duration, Instant};

use compact_str::CompactString;
use yellowstone_grpc_proto::prelude::SubscribeUpdateTransaction;

use crate::plugins::pump::models::events::TradeEventExtended;
// 移除对PUMP_PROGRAM_ID的导入
// use crate::core::PUMP_PROGRAM_ID;

// 导出配置模块
mod config;
pub use config::*;

/// PumpFun交易类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum PumpTransactionType {
    /// 初始化
    Initialize,
    /// 设置参数
    SetParams,
    /// 创建代币
    Create,
    /// 购买代币
    Buy,
    /// 出售代币
    Sell,
    /// 提取
    Withdraw,
    /// 未知类型
    Unknown,
}

impl From<&str> for PumpTransactionType {
    fn from(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "initialize" => Self::Initialize,
            "setparams" => Self::SetParams,
            "create" => Self::Create,
            "buy" => Self::Buy,
            "sell" => Self::Sell,
            "withdraw" => Self::Withdraw,
            _ => Self::Unknown,
        }
    }
}

/// 交换方向
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum SwapDirection {
    /// 买入（SOL -> Token）
    Buy,
    /// 卖出（Token -> SOL）
    Sell,
}

/// 代币信息
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TokenInfo {
    /// 代币mint地址
    pub mint: Pubkey,
    /// 代币名称
    pub name: Option<String>,
    /// 代币符号
    pub symbol: Option<String>,
    /// 代币URI
    pub uri: Option<String>,
    /// 小数位数
    pub decimals: u8,
    /// 总供应量
    pub total_supply: Option<u64>,
}

/// 价格信息
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct PriceInfo {
    /// 当前价格（SOL/Token）
    pub current_price: Option<Decimal>,
    /// 虚拟SOL储备
    pub virtual_sol_reserves: u64,
    /// 虚拟代币储备
    pub virtual_token_reserves: u64,
    /// 真实SOL储备
    pub real_sol_reserves: Option<u64>,
    /// 真实代币储备
    pub real_token_reserves: Option<u64>,
}

/// 交易费用信息
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct FeeInfo {
    /// 费用接收者
    pub fee_recipient: Option<Pubkey>,
    /// 费用基点
    pub fee_basis_points: Option<u64>,
    /// 费用金额
    pub fee_amount: Option<u64>,
    /// 创作者费用接收者
    pub creator_fee_recipient: Option<Pubkey>,
    /// 创作者费用基点
    pub creator_fee_basis_points: Option<u64>,
    /// 创作者费用金额
    pub creator_fee_amount: Option<u64>,
    /// 创作者金库地址
    pub creator_vault: Option<Pubkey>,
}

/// 交易详情
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct TransactionDetails {
    /// 交易签名
    pub signature: String,
    /// 交易类型
    pub transaction_type: PumpTransactionType,
    /// 用户地址
    pub user: Option<Pubkey>,
    /// 代币信息
    pub token_info: Option<TokenInfo>,
    /// 交换方向
    pub swap_direction: Option<SwapDirection>,
    /// SOL金额（lamports）
    pub sol_amount: Option<u64>,
    /// 代币金额
    pub token_amount: Option<u64>,
    /// 价格信息
    pub price_info: Option<PriceInfo>,
    /// 费用信息
    pub fee_info: Option<FeeInfo>,
    /// 时间戳
    pub timestamp: Option<DateTime<Utc>>,
    /// 是否完成
    pub is_complete: bool,
}

impl Default for TransactionDetails {
    fn default() -> Self {
        Self {
            signature: String::new(),
            transaction_type: PumpTransactionType::Unknown,
            user: None,
            token_info: None,
            swap_direction: None,
            sol_amount: None,
            token_amount: None,
            price_info: None,
            fee_info: None,
            timestamp: None,
            is_complete: false,
        }
    }
}

/// 流水线配置
#[derive(Clone, Debug)]
pub struct PipelineConfig {
    pub grpc_buffer_size: usize,
    pub parser_threads: usize,
    pub publisher_threads: usize,
    pub queue_capacity: usize,
    pub redis_channel: String,
    pub bonk_channel: String,
    pub pump_channel: String,
}

impl Default for PipelineConfig {
    fn default() -> Self {
        Self {
            grpc_buffer_size: 100000,
            parser_threads: num_cpus::get() * 2,
            publisher_threads: num_cpus::get(),
            queue_capacity: 1000000,
            redis_channel: "trades_channel".to_string(),
            bonk_channel: "bonk_channel".to_string(),
            pump_channel: "pump_channel".to_string(),
        }
    }
}

// 引用 parsers 模块中的 RawTransaction
pub use crate::core::parsers::RawTransaction;

/// 预序列化的解析事件
#[derive(Clone, Debug)]
pub struct ParsedEvent {
    pub event: TradeEventExtended,
    pub json: CompactString,
    pub timestamp: u64,
    pub publish_start: Instant,
}

/// 统一发布事件 - 支持任意格式的内容
#[derive(Clone, Debug)]
pub struct UnifiedEvent {
    /// 发布内容（任意格式的字符串）
    pub content: CompactString,
    /// 时间戳
    pub timestamp: u64,
    /// 发布开始时间（用于性能统计）
    pub publish_start: Instant,
    /// 插件名称（用于统计）
    pub plugin_name: String,
}

/// 实时监控指标
pub struct RealTimeMetrics {
    // 吞吐量
    pub received_count: AtomicU64,
    pub parsed_count: AtomicU64,
    pub published_count: AtomicU64,
    
    // 延迟（纳秒）
    pub parse_time_total: AtomicU64,
    pub publish_time_total: AtomicU64,
    pub end_to_end_time: AtomicU64,
    
    // 错误计数
    pub parse_errors: AtomicU64,
    pub publish_errors: AtomicU64,
    pub queue_overflow: AtomicU64,
    pub publish_queue_overflow: AtomicU64,
    
    // 队列深度
    pub parse_queue_depth: AtomicUsize,
    pub publish_queue_depth: AtomicUsize,
    
    // 启动时间
    pub start_time: Instant,
}

impl Default for RealTimeMetrics {
    fn default() -> Self {
        Self {
            received_count: AtomicU64::new(0),
            parsed_count: AtomicU64::new(0),
            published_count: AtomicU64::new(0),
            parse_time_total: AtomicU64::new(0),
            publish_time_total: AtomicU64::new(0),
            end_to_end_time: AtomicU64::new(0),
            parse_errors: AtomicU64::new(0),
            publish_errors: AtomicU64::new(0),
            queue_overflow: AtomicU64::new(0),
            publish_queue_overflow: AtomicU64::new(0),
            parse_queue_depth: AtomicUsize::new(0),
            publish_queue_depth: AtomicUsize::new(0),
            start_time: Instant::now(),
        }
    }
}

impl RealTimeMetrics {
    pub fn new() -> Self {
        Self::default()
    }
    
    pub fn start_reporting(&self) {
        let metrics = unsafe { &*(self as *const Self) };
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));  // 改为60秒一次，只收集数据不输出
            let mut last_received = 0;
            let mut last_parsed = 0;
            let mut last_published = 0;
            
            // 初始化但不输出日志
            
            loop {
                interval.tick().await;
                
                // 只收集数据，不输出日志
                let received = metrics.received_count.load(Ordering::Relaxed);
                let parsed = metrics.parsed_count.load(Ordering::Relaxed);
                let published = metrics.published_count.load(Ordering::Relaxed);
                
                last_received = received;
                last_parsed = parsed;
                last_published = published;
                
                // 不输出任何日志，避免干扰插件统计信息
            }
        });
    }
}
