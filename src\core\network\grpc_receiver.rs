/// gRPC接收服务
///
/// 负责从gRPC流接收交易数据

use std::sync::Arc;
use std::sync::atomic::Ordering;
use std::time::Duration;
use std::collections::HashMap;

use tokio::sync::mpsc;
use tokio_stream::StreamExt;

use log::{info, error, warn, debug};
use anyhow::Result;

use yellowstone_grpc_proto::prelude::*;
use yellowstone_grpc_proto::prelude::subscribe_update::UpdateOneof;
use yellowstone_grpc_client::GeyserGrpcClient;

use crate::core::parsers::RawTransaction;
use crate::core::types::RealTimeMetrics;
use crate::core::types::get_config;
// use crate::core::PUMP_PROGRAM_ID; // 不再使用硬编码常量

/// 无阻塞gRPC接收器
pub struct GrpcReceiver {
    sender: crossbeam_channel::Sender<RawTransaction>,
    metrics: Arc<RealTimeMetrics>,
    max_retries: u32,
    retry_delay_ms: u64,
}

impl GrpcReceiver {
    pub fn new(sender: crossbeam_channel::Sender<RawTransaction>, metrics: Arc<RealTimeMetrics>) -> Self {
        Self { 
            sender, 
            metrics,
            max_retries: 10,
            retry_delay_ms: 5000,
        }
    }
    
    /// 启动gRPC接收器，带自动重试机制
    pub async fn start_with_retry(&self, endpoint: &str) -> Result<()> {
        let mut retry_count = 0;
        let mut consecutive_errors = 0;
        let consecutive_error_threshold = 3;
        
        info!("[连接] 启动gRPC接收器，连接端点: {}", endpoint);
        
        loop {
            // 仅在首次连接时详细记录
            if retry_count == 0 {
                info!("[连接] 尝试连接gRPC端点 (重试 {}/{})", retry_count, self.max_retries);
            }
            
            match self.connect_and_run(endpoint).await {
                Ok(_) => {
                    info!("[连接] gRPC连接正常关闭");
                    consecutive_errors = 0;
                    break;
                }
                Err(e) => {
                    retry_count += 1;
                    consecutive_errors += 1;
                    
                    // 仅详细记录前几次错误或者每隔几次
                    if consecutive_errors <= consecutive_error_threshold || consecutive_errors % 3 == 0 {
                        error!("[连接] gRPC连接失败 (重试 {}/{}): {}", retry_count, self.max_retries, e);
                    } else {
                        // 简化的错误信息
                        error!("[连接] gRPC连接持续失败 (重试 {}/{})", retry_count, self.max_retries);
                    }
                    
                    if retry_count >= self.max_retries {
                        error!("[连接] 达到最大重试次数，停止尝试");
                        return Err(e);
                    }
                    
                    if retry_count <= 3 {
                        warn!("[连接] {}秒后重试连接...", self.retry_delay_ms / 1000);
                    }
                    tokio::time::sleep(Duration::from_millis(self.retry_delay_ms)).await;
                }
            }
        }
        
        Ok(())
    }
    
    /// 连接并运行gRPC流
    async fn connect_and_run(&self, endpoint: &str) -> Result<()> {
        // 使用参考资料中的连接方式
        let mut client = GeyserGrpcClient::build_from_shared(endpoint.to_string())?
            .connect_timeout(Duration::from_secs(10))
            .timeout(Duration::from_secs(10))
            .tls_config(tonic::transport::channel::ClientTlsConfig::new().with_native_roots())?
            .max_decoding_message_size(1024 * 1024 * 1024)
            .connect()
            .await?;
        
        let mut transactions = HashMap::new();
        
        // 订阅Pump交易
        transactions.insert(
            "pump_transactions".to_string(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                account_include: vec![get_config().programs.pump_program.to_string()],
                account_exclude: vec![],
                account_required: vec![],
                signature: None,
            },
        );
        
        // 订阅Bonk交易
        transactions.insert(
            "bonk_transactions".to_string(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                account_include: vec![get_config().programs.bonk_program.to_string()],
                account_exclude: vec![],
                account_required: vec![],
                signature: None,
            },
        );
        
        info!("[连接] gRPC连接成功");
        
        // 使用正确的订阅方式
        let subscription_request = SubscribeRequest {
            accounts: HashMap::default(),
            slots: HashMap::default(),
            transactions,
            transactions_status: HashMap::default(),
            blocks: HashMap::default(),
            blocks_meta: HashMap::default(),
            entry: HashMap::default(),
            commitment: Some(CommitmentLevel::Processed as i32),
            accounts_data_slice: vec![],
            ping: None,
            from_slot: None,
        };
        
        // 设置订阅
        let (mut _subscribe_tx, mut stream) = client.subscribe_with_request(Some(subscription_request)).await?;
        
        info!("[接收] gRPC流已打开，开始接收交易");
        
        let mut error_count = 0;
        let error_log_threshold = 5; // 每收到5个错误才记录一次
        
        // 接收流数据
        'main_loop: while let Some(message) = stream.next().await {
            match message {
                Ok(msg) => {
                    if let Some(UpdateOneof::Transaction(tx)) = msg.update_oneof {
                        let receive_time = std::time::Instant::now();
                        let recv_timestamp_us = std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap_or_default()
                            .as_micros() as u64;
                        
                        // 在独立线程中处理每个交易 - 零延迟
                        if let Some(raw_tx) = RawTransaction::from_update(tx, recv_timestamp_us) {
                            let deserialize_time = receive_time.elapsed().as_micros();

                            
                            // 非阻塞发送
                            if let Err(_) = self.sender.send(raw_tx.clone()) {
                                error!("发送队列已关闭");
                                break 'main_loop;
                            }
                            
                            let total_time = receive_time.elapsed().as_micros();

                            
                            // 更新接收指标
                            self.metrics.received_count.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                            self.metrics.parse_queue_depth.fetch_add(1, std::sync::atomic::Ordering::Relaxed);
                        }
                    }
                }
                Err(e) => {
                    error_count += 1;
                    
                    // 仅每隔几次记录详细错误
                    if error_count <= 2 || error_count % error_log_threshold == 0 {
                        error!("[错误] gRPC流错误: {:?}", e);
                    }
                    
                    // 流错误时退出，让上层重试
                    return Err(e.into());
                }
            }
        }
        
        info!("[接收] gRPC流已关闭");
        Ok(())
    }
    
    /// 创建订阅请求
    fn create_subscription_request(&self) -> SubscribeRequest {
        let mut transactions = HashMap::new();
        
        transactions.insert(
            "pump_transactions".to_string(),
            SubscribeRequestFilterTransactions {
                vote: Some(false),
                failed: Some(false),
                account_include: vec![get_config().programs.pump_program.to_string()],
                account_exclude: vec![],
                account_required: vec![],
                signature: None,
            },
        );
        
        SubscribeRequest {
            accounts: HashMap::default(),
            slots: HashMap::default(),
            transactions,
            transactions_status: HashMap::default(),
            blocks: HashMap::default(),
            blocks_meta: HashMap::default(),
            entry: HashMap::default(),
            commitment: Some(CommitmentLevel::Processed as i32),
            accounts_data_slice: vec![],
            ping: None,
            from_slot: None,
        }
    }
    
    /// 处理gRPC消息
    async fn handle_message(&self, msg: SubscribeUpdate) -> Result<()> {
        match msg.update_oneof {
            Some(UpdateOneof::Transaction(tx_update)) => {
                let recv_timestamp_us = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_micros() as u64;
                if let Some(raw_tx) = RawTransaction::from_update(tx_update, recv_timestamp_us) {
                    // 无阻塞发送 - 如果队列满则丢弃
                    match self.sender.try_send(raw_tx) {
                        Ok(_) => {
                            self.metrics.received_count.fetch_add(1, Ordering::Relaxed);
                            self.metrics.parse_queue_depth.fetch_add(1, Ordering::Relaxed);
                        }
                        Err(_) => {
                            self.metrics.queue_overflow.fetch_add(1, Ordering::Relaxed);
                        }
                    }
                }
            }
            Some(UpdateOneof::Ping(_)) => {
                // 处理ping但不阻塞
            }
            Some(UpdateOneof::Pong(_)) => {}
            None => {
                error!("[错误] 更新中未找到内容");
            }
            _ => {}
        }
        
        Ok(())
    }
}