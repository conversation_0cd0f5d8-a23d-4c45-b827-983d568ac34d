/// 零延迟多协议解析器 - 插件架构
/// 
/// 提供完整的多协议解析功能，支持PumpFun、Raydium等
/// 采用插件式架构，零延迟处理


// 新增模块化架构
pub mod core;
pub mod plugins;

// 重新导出主要类型和函数
pub use core::{PumpTransactionType, SwapDirection, TokenInfo, PriceInfo, PipelineConfig, RealTimeMetrics, UltimatePipeline};

// 导出模块化架构
pub use core::*;
pub use plugins::*;

// 解析器版本信息
pub const PARSER_VERSION: &str = "1.0.0";
pub const PARSER_BUILD_DATE: &str = env!("CARGO_PKG_VERSION");

/// 获取解析器版本和支持功能信息
pub fn get_parser_info() -> String {
    format!(
        "多协议解析器 v{}\n支持功能:\n- PumpFun协议解析\n- Bonk协议解析\n- 零延迟处理\n- 插件式架构",
        PARSER_VERSION
    )
}
