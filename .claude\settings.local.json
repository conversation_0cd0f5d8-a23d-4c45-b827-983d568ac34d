{"permissions": {"allow": ["Bash(find /mnt/d/personal/Desktop/pumpjiexi/参考解析代码 -name \"*.rs\" -exec grep -l \"creator.*vault\\|vault.*creator\" {} ;)", "Bash(find \"/mnt/d/personal/Desktop/pumpjiexi/参考解析代码\" -name \"*.rs\" -exec grep -l \"creator.*vault\\|vault.*creator\" {} ;)", "Bash(cd \"/mnt/d/personal/Desktop/pumpjiexi/参考解析代码\")", "Bash(find . -name \"*.rs\" -exec grep -l \"creator_vault\" {} ;)", "Bash(find . -name \"README.md\" -exec grep -l -i \"address\\|vault\\|creator\\|pda\" {} ;)", "Bash(find /mnt/host/d/personal -name \"*.py\" -type f)", "Bash(ls -la /mnt/host/d/personal)", "Bash(find /mnt/d/personal -name \"*.py\" -type f)", "Bash(mkdir -p /mnt/d/personal/Desktop/pumpjiexi/src/modules /mnt/d/personal/Desktop/pumpjiexi/src/parsers/bonk_parser/{models,decoders})", "Bash(cp /mnt/d/personal/Desktop/pumpjiexi/bonkjiexi/bonk-/src/models/trade_event.rs /mnt/d/personal/Desktop/pumpjiexi/src/parsers/bonk_parser/models/)", "Bash(cp /mnt/d/personal/Desktop/pumpjiexi/bonkjiexi/bonk-/src/models/mod.rs /mnt/d/personal/Desktop/pumpjiexi/src/parsers/bonk_parser/models/)", "Bash(cp /mnt/d/personal/Desktop/pumpjiexi/bonkjiexi/bonk-/src/decoders/raydium.rs /mnt/d/personal/Desktop/pumpjiexi/src/parsers/bonk_parser/decoders/)", "Bash(cp /mnt/d/personal/Desktop/pumpjiexi/bonkjiexi/bonk-/src/decoders/mod.rs /mnt/d/personal/Desktop/pumpjiexi/src/parsers/bonk_parser/decoders/)", "Bash(cp /mnt/d/personal/Desktop/pumpjiexi/bonkjiexi/bonk-/src/config.rs /mnt/d/personal/Desktop/pumpjiexi/src/parsers/bonk_parser/)", "Bash(mkdir -p /mnt/d/personal/Desktop/pumpjiexi/src/core /mnt/d/personal/Desktop/pumpjiexi/src/plugins/{pump,bonk})", "Bash(mv /mnt/d/personal/Desktop/pumpjiexi/src/modules/* /mnt/d/personal/Desktop/pumpjiexi/src/core/)", "Bash(cp -r /mnt/d/personal/Desktop/pumpjiexi/src/parsers/bonk_parser/* /mnt/d/personal/Desktop/pumpjiexi/src/plugins/bonk/)", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "Bash(cargo check:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(where pkg-config)", "Bash(ls:*)", "Bash(echo $PKG_CONFIG_PATH)", "Bash(rustc:*)", "Bash(powershell.exe:*)", "WebFetch(domain:github.com)", "Bash(OPENSSL_STATIC=1 OPENSSL_LIB_DIR=\"C:\\Program Files\\OpenSSL-Win64\\lib\\VC\\x64\\MD\" OPENSSL_INCLUDE_DIR=\"C:\\Program Files\\OpenSSL-Win64\\include\" OPENSSL_LIBS=\"libssl_static:libcrypto_static\" powershell.exe -Command \"cargo run --release\")", "Bash(cargo build:*)", "Bash(export:*)", "Bash(cargo --version)", "Bash(grep:*)", "Bash(find:*)"], "deny": []}}